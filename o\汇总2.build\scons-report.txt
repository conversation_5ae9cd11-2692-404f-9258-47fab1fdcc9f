AS=as
CC="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\downloads\ccache\v4.6\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\downloads\gcc\x86_64\11.3.0-14.0.3-10.0.0-msvcrt-r3\mingw64\bin\gcc.exe"
CCACHE_DIR=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
CCACHE_LOGFILE=C:\Users\<USER>\Desktop\新建文~3\o\汇总2.build\ccache-25892.txt
CCCOM=$CC -o $TARGET -c $CFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CFILESUFFIX=.c
CPPDEFINES=['_WIN32_WINNT=0x0501', '__NUITKA_NO_ASSERT__', '_NUITKA_STANDALONE', 'MS_WIN64', '_NUITKA_CONSTANTS_FROM_RESOURCE', '_NUITKA_FROZEN=155', '_NUITKA_EXE', '_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1']
CPPDEFPREFIX=-D
CPPDEFSUFFIX=
CPPPATH=['D:\\python\\include', '.', 'D:\\python\\Lib\\SITE-P~1\\nuitka\\build\\include', 'D:\\python\\Lib\\SITE-P~1\\nuitka\\build\\static_src']
CPPSUFFIXES=['.c', '.C', '.cxx', '.cpp', '.c++', '.cc', '.h', '.H', '.hxx', '.hpp', '.hh', '.F', '.fpp', '.FPP', '.m', '.mm', '.S', '.spp', '.SPP', '.sx']
CXX="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\downloads\ccache\v4.6\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\downloads\gcc\x86_64\11.3.0-14.0.3-10.0.0-msvcrt-r3\mingw64\bin\gcc.exe"
CXXCOM=$CXX -o $TARGET -c $CXXFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CXXFILESUFFIX=.cc
HOST_ARCH=x86_64
HOST_OS=win32
INCPREFIX=-I
INCSUFFIX=
LDMODULE=$SHLINK
LDMODULEFLAGS=$SHLINKFLAGS
LDMODULENAME=${LDMODULEPREFIX}$_get_ldmodule_stem${_LDMODULESUFFIX}
LDMODULEPREFIX=$SHLIBPREFIX
LDMODULESUFFIX=$SHLIBSUFFIX
LDMODULEVERSION=$SHLIBVERSION
LDMODULE_NOVERSION_SYMLINK=$_get_shlib_dir${LDMODULEPREFIX}$_get_ldmodule_stem${LDMODULESUFFIX}
LDMODULE_SONAME_SYMLINK=$_get_shlib_dir$_LDMODULESONAME
LIBDIRPREFIX=-L
LIBDIRSUFFIX=
LIBLINKPREFIX=-l
LIBLINKSUFFIX=
LIBPATH=['D:\\python\\libs']
LIBPREFIX=lib
LIBPREFIXES=['$LIBPREFIX']
LIBS=['m', 'python311']
LIBSUFFIX=.a
LIBSUFFIXES=['$LIBSUFFIX']
LINK="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\downloads\gcc\x86_64\11.3.0-14.0.3-10.0.0-msvcrt-r3\mingw64\bin\gcc.exe"
LINKCOM=$LINK -o $TARGET $LINKFLAGS $__RPATH @".\@link_input.txt" $_LIBDIRFLAGS $_LIBFLAGS
OBJPREFIX=
OBJSUFFIX=.o
PLATFORM=win32
PROGPREFIX=
PROGSUFFIX=.exe
RC=windres
RCCOM=$RC $_CPPDEFFLAGS $RCINCFLAGS ${RCINCPREFIX} ${SOURCE.dir} $RCFLAGS -i $SOURCE -o $TARGET
RCINCFLAGS=${_concat(RCINCPREFIX, CPPPATH, RCINCSUFFIX, __env__, RDirs, TARGET, SOURCE, affect_signature=False)}
RCINCPREFIX=--include-dir 
RCINCSUFFIX=
RPATHPREFIX=-Wl,-rpath=
RPATHSUFFIX=
SHCC=$CC
SHCCCOM=$SHCC -o $TARGET -c $SHCFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHCXX=$CXX
SHCXXCOM=$SHCXX -o $TARGET -c $SHCXXFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHELL=C:\Windows\System32\cmd.exe
SHLIBNAME=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${_SHLIBSUFFIX}
SHLIBPREFIX=
SHLIBSONAMEFLAGS=-Wl,-soname=$_SHLIBSONAME
SHLIBSUFFIX=.dll
SHLIB_NOVERSION_SYMLINK=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${SHLIBSUFFIX}
SHLIB_SONAME_SYMLINK=${_get_shlib_dir}$_SHLIBSONAME
SHLINK=$LINK
SHOBJPREFIX=$OBJPREFIX
SHOBJSUFFIX=.o
TARGET_ARCH=x86_64
TEMPFILEARGJOIN= 
TEMPFILEPREFIX=@
TOOLS=['mingw', 'gcc', 'g++', 'gnulink']
WINDOWSDEFPREFIX=
WINDOWSDEFSUFFIX=.def
gcc_mode=True
clang_mode=False
msvc_mode=False
mingw_mode=True
clangcl_mode=False
PATH=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\downloads\gcc\x86_64\11.3.0-14.0.3-10.0.0-msvcrt-r3\mingw64\bin;e:\vm\bin\;c:\windows\system32;c:\windows;c:\windows\system32\wbem;c:\windows\system32\windowspowershell\v1.0\;c:\windows\system32\openssh\;e:\sybase\win32;e:\shared\win32;e:\shared\sybase central 4.1;e:\program files (x86)\microsoft sql server\100\tools\binn\;e:\program files\microsoft sql server\100\tools\binn\;e:\program files\microsoft sql server\100\dts\binn\;e:\program files (x86)\microsoft sql server\100\tools\binn\vsshell\common7\ide\;e:\program files (x86)\microsoft sql server\100\dts\binn\;e:\sybase\shared\powerbuilder;e:\svn\bin;c:\android;c:\windows\system32;e:\shared\powerdynamo\win32;c:\program files\nvidia corporation\nvidia nvdlisr;c:\users\<USER>\.jdks\corretto-1.8.0_312\bin;e:\node\;F:\conda\Scripts;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;F:\exp32code\tools\idf-git\2.30.1\cmd;C:\Users\<USER>\Desktop\33\instantclient_12_2;D:\python;D:\python\Scripts;F:\py\Library\mingw-w64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;E:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm
