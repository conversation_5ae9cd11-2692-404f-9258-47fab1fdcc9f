#ifndef __NUITKA_GLOBAL_CONSTANTS_H__
#define __NUITKA_GLOBAL_CONSTANTS_H__

extern PyObject *global_constants[89];
// ()
#define const_tuple_empty global_constants[0]
// {}
#define const_dict_empty global_constants[1]
// 0
#define const_int_0 global_constants[2]
// 1
#define const_int_pos_1 global_constants[3]
// -1
#define const_int_neg_1 global_constants[4]
// 0.0
#define const_float_0_0 global_constants[5]
// -0.0
#define const_float_minus_0_0 global_constants[6]
// 1.0
#define const_float_1_0 global_constants[7]
// -1.0
#define const_float_minus_1_0 global_constants[8]
// 0
#define const_int_0 global_constants[2]
// ''
#define const_str_empty global_constants[9]
// b''
#define const_bytes_empty global_constants[10]
// '__module__'
#define const_str_plain___module__ global_constants[11]
// '__class__'
#define const_str_plain___class__ global_constants[12]
// '__name__'
#define const_str_plain___name__ global_constants[13]
// '__package__'
#define const_str_plain___package__ global_constants[14]
// '__metaclass__'
#define const_str_plain___metaclass__ global_constants[15]
// '__abstractmethods__'
#define const_str_plain___abstractmethods__ global_constants[16]
// '__dict__'
#define const_str_plain___dict__ global_constants[17]
// '__doc__'
#define const_str_plain___doc__ global_constants[18]
// '__file__'
#define const_str_plain___file__ global_constants[19]
// '__path__'
#define const_str_plain___path__ global_constants[20]
// '__enter__'
#define const_str_plain___enter__ global_constants[21]
// '__exit__'
#define const_str_plain___exit__ global_constants[22]
// '__builtins__'
#define const_str_plain___builtins__ global_constants[23]
// '__all__'
#define const_str_plain___all__ global_constants[24]
// '__init__'
#define const_str_plain___init__ global_constants[25]
// '__cmp__'
#define const_str_plain___cmp__ global_constants[26]
// '__iter__'
#define const_str_plain___iter__ global_constants[27]
// '__loader__'
#define const_str_plain___loader__ global_constants[28]
// '__compiled__'
#define const_str_plain___compiled__ global_constants[29]
// '__nuitka__'
#define const_str_plain___nuitka__ global_constants[30]
// 'inspect'
#define const_str_plain_inspect global_constants[31]
// 'compile'
#define const_str_plain_compile global_constants[32]
// 'range'
#define const_str_plain_range global_constants[33]
// 'open'
#define const_str_plain_open global_constants[34]
// 'super'
#define const_str_plain_super global_constants[35]
// 'sum'
#define const_str_plain_sum global_constants[36]
// 'format'
#define const_str_plain_format global_constants[37]
// '__import__'
#define const_str_plain___import__ global_constants[38]
// 'bytearray'
#define const_str_plain_bytearray global_constants[39]
// 'staticmethod'
#define const_str_plain_staticmethod global_constants[40]
// 'classmethod'
#define const_str_plain_classmethod global_constants[41]
// 'keys'
#define const_str_plain_keys global_constants[42]
// 'name'
#define const_str_plain_name global_constants[43]
// 'globals'
#define const_str_plain_globals global_constants[44]
// 'locals'
#define const_str_plain_locals global_constants[45]
// 'fromlist'
#define const_str_plain_fromlist global_constants[46]
// 'level'
#define const_str_plain_level global_constants[47]
// 'read'
#define const_str_plain_read global_constants[48]
// 'rb'
#define const_str_plain_rb global_constants[49]
// '/'
#define const_str_slash global_constants[50]
// '\\'
#define const_str_backslash global_constants[51]
// 'path'
#define const_str_plain_path global_constants[52]
// 'basename'
#define const_str_plain_basename global_constants[53]
// 'abspath'
#define const_str_plain_abspath global_constants[54]
// 'isabs'
#define const_str_plain_isabs global_constants[55]
// 'exists'
#define const_str_plain_exists global_constants[56]
// 'isdir'
#define const_str_plain_isdir global_constants[57]
// 'isfile'
#define const_str_plain_isfile global_constants[58]
// 'listdir'
#define const_str_plain_listdir global_constants[59]
// 'getattr'
#define const_str_plain_getattr global_constants[60]
// '__cached__'
#define const_str_plain___cached__ global_constants[61]
// 'print'
#define const_str_plain_print global_constants[62]
// 'end'
#define const_str_plain_end global_constants[63]
// 'file'
#define const_str_plain_file global_constants[64]
// 'bytes'
#define const_str_plain_bytes global_constants[65]
// '.'
#define const_str_dot global_constants[66]
// '__loader__'
#define const_str_plain___loader__ global_constants[28]
// 'send'
#define const_str_plain_send global_constants[67]
// 'throw'
#define const_str_plain_throw global_constants[68]
// 'close'
#define const_str_plain_close global_constants[69]
// 'site'
#define const_str_plain_site global_constants[70]
// 'type'
#define const_str_plain_type global_constants[71]
// 'len'
#define const_str_plain_len global_constants[72]
// 'range'
#define const_str_plain_range global_constants[33]
// 'repr'
#define const_str_plain_repr global_constants[73]
// 'int'
#define const_str_plain_int global_constants[74]
// 'iter'
#define const_str_plain_iter global_constants[75]
// '__spec__'
#define const_str_plain___spec__ global_constants[76]
// '_initializing'
#define const_str_plain__initializing global_constants[77]
// 'parent'
#define const_str_plain_parent global_constants[78]
// 'types'
#define const_str_plain_types global_constants[79]
// '__main__'
#define const_str_plain___main__ global_constants[80]
// 'as_file'
#define const_str_plain_as_file global_constants[81]
// 'register'
#define const_str_plain_register global_constants[82]
// '__class_getitem__'
#define const_str_plain___class_getitem__ global_constants[83]
// '__match_args__'
#define const_str_plain___match_args__ global_constants[84]
// '__aenter__'
#define const_str_plain___aenter__ global_constants[85]
// '__aexit__'
#define const_str_plain___aexit__ global_constants[86]
// 'fileno'
#define const_str_plain_fileno global_constants[87]
#endif
