
import pandas as pd
import os
import shutil
def match_and_move_folders(source_folder, excel_path):
    df = pd.read_excel(excel_path)
    institution_dict = {}
    for index, row in df.iterrows():
        institution_dict[row['机构名称']] = row['机构区划']
    
    for folder_name in os.listdir(source_folder):
        folder_path = os.path.join(source_folder, folder_name)
        
        if not os.path.isdir(folder_path):
            continue
        matched = False
        for institution_name in institution_dict.keys():
            if institution_name in folder_name:
                matched = True
                partition = institution_dict[institution_name]
                target_folder = os.path.join(source_folder, partition)
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder)
                shutil.move(folder_path, os.path.join(target_folder, folder_name))
                print(f"文件夹 {folder_name} 已移动到 {target_folder}")
                break
        
        # 如果没有匹配到
        if not matched:
            print(f"文件夹 {folder_name} 未能匹配到任何机构名称，未进行移动。")

source_folder = r'C:\Users\<USER>\Desktop\datawork\新建文件夹'
excel_path = r'E:\WeChat Files\WeChat Files\WeChat Files\FFbb11\FileStorage\File\2025-06\医疗机构名单(1).xlsx'
match_and_move_folders(source_folder, excel_path)
