import duckdb
import os

def csv_xlsx(csv_dict, save_path, split_by_hospital=True,sava_type = 'xlsx', hospital_column="定点机构名称"):
    con = duckdb.connect(database=':memory:')
    con.install_extension('spatial')  # 加载空间扩展
    con.load_extension('spatial')
    con.sql(f"""SET temp_directory = 'g:/temp.tmp'""")
    # 加载数据字典
    con.sql(rf"""CREATE TABLE datatable AS
    SELECT * FROM st_read('{os.path.join(os.getcwd(), '中英对照.xlsx')}', open_options = ['HEADERS=FORCE', 'FIELD_TYPES=STRING'])""")

    # 获取用户输入的文件地址
    #file_path = input("请输入文件地址：")
    for sava_name, file_path in csv_dict.items():
        a = f"""
        CREATE table a AS
        select
            *
        from
            read_csv_auto({file_path},
            all_varchar = TRUE)
        """
        print(a)
        try:
            con.sql(a)
        except Exception as e:
            print(a)
            print(e)
        # 对数据进行处理
        df = con.execute('SELECT d.*,a.column_name FROM datatable d left join (DESCRIBE select * FROM a) a on d.A = a.column_name where a.column_name is not null').df()
        #print(df)
        # 循环遍历df的每一行. 修改表a的列名为 datatable.b  
        for index, row in df.iterrows():
            xxx = f"""ALTER TABLE a RENAME COLUMN {row['column_name']} TO {row['B']}"""
            con.sql(xxx)   
        #判断有没有uuid
        EXCLUDE = """ EXCLUDE (uuid) """  if df['A'].isin(['uuid']).any() else ""
        if not split_by_hospital:
            # 如果不按医疗机构分割，直接保存整个表
            output_file = os.path.join(save_path, sava_name)
            if sava_type == 'xlsx':
                sql_name = f""" COPY (select * {EXCLUDE} from a) to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
            elif sava_type == 'csv':
                sql_name = f""" COPY (select * {EXCLUDE} from a) to '{output_file}.csv'"""
            #print(sql_name)
            con.sql(sql_name)
        else:
            # 按医疗机构分割并保存
            ifname = con.sql(f"""SELECT COUNT(*)
               FROM information_schema.columns
               WHERE table_name = 'a' AND column_name = '{hospital_column}' """).fetchall()
            
            if ifname[0][0] == 0:
                print(f'{sava_name}错误：缺少{hospital_column}列')
            else:

                results = con.sql(f"""select DISTINCT trim({hospital_column}) from a""").fetchall()
                if  hospital_column == '定点机构编码':
                    results = con.sql(f"""select * from (
    SELECT *,ROW_NUMBER() OVER (PARTITION BY 定点机构编码 order by 定点机构名称 desc) as rn FROM (
    select distinct 定点机构名称,定点机构编码 from a)) where rn = 1 """).fetchall()
                for row in results:
                    output_dir = os.path.join(save_path, row[0].strip())
                    output_file = os.path.join(output_dir, sava_name)
                    #文件名加上医院名称
                    output_file = os.path.join(output_dir, row[0].strip() + sava_name.strip())
                    try:
                        os.makedirs(output_dir, exist_ok=True)
                    except Exception as e:
                        print(e)
                        print(output_dir)
                        continue
                    # 保存处理后的数据到新的文件中
                    #output_file = file_path.replace('.csv', '')
                    if  hospital_column == '定点机构编码':
                        sql_name = f""" COPY (select * {EXCLUDE} from a where 定点机构编码 = '{row[1]}') to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""
                    else:
                        sql_name = f""" COPY (select * {EXCLUDE} from a where trim({hospital_column}) = '{row[0]}') to '{output_file}.xlsx' WITH (FORMAT GDAL, DRIVER 'xlsx')"""

                    con.sql(sql_name)
        con.sql("""drop table a""")
    # 关闭数据库连
    con.close()
if __name__ == '__main__':
#     csv_dict = {
#     '煎药': r"""'D:\xzz\423\1111\煎药.csv'"""
# }
#     save_path = r'C:\Users\<USER>\Desktop\数据\拆分\\'

    path = r'C:\Users\<USER>\Desktop\datawork\新建文件夹 (2)/'
    save_path = r'C:\Users\<USER>\Desktop\datawork\新建文件夹 (2)\C/'
    csv_files = [f for f in os.listdir(path) if f.endswith('.csv')]
    csv_dict = {}
# 按前缀将CSV文件分组
    for csv_file in csv_files:
        prefix = csv_file.split('_page_')[0] if '_page_' in csv_file else os.path.splitext(csv_file)[0]
        csv_dict.setdefault(prefix, []).append(os.path.join(path, csv_file))

    print( csv_dict)
    # 示例1：不按医疗机构分割保存
    #csv_xlsx(csv_dict, save_path, split_by_hospital=False)
    csv_xlsx(csv_dict, save_path, split_by_hospital=True)
    # 示例2：按默认的"医疗机构名称"列分割保存
    #csv_xlsx(csv_dict, save_path, split_by_hospital=True, hospital_column="定点机构编码")

    # 示例3：按自定义列名"医院名称"分割保存
    # csv_xlsx(csv_dict, save_path, split_by_hospital=True, hospital_column="医院名称")