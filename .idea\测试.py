import re
import subprocess
from time import sleep
from multiprocessing import Pool
from playwright.async_api import expect
from playwright.sync_api import sync_playwright
import pandas as pd
import pandas.io.clipboard as cb
from loguru import logger

def copy_sql_query(page, sql_query):
    # 在文本框中粘贴SQL查询
    textarea = page.locator('textarea.inputarea')
    cb.copy(sql_query)
    textarea.press("Control+a")
    textarea.press("Control+v")

def run_sql_query(page):
    # 单击“运行”按钮
    page.locator("[data-tracker-key='toolbar-button-run']").click()
    # 等待执行结果出现
    while True:
        message_title = page.wait_for_selector(".next-message-title", timeout=3000000)
       # Check if the message_title element exists before accessing its text_content
        if message_title and message_title.text_content() == '执行结束':
            break

def download_query_result(page):
    # 等待结果加载并获取行数
    try:
        page.wait_for_selector(".excel-bar-right", timeout=10000)
    except:
        return -1
    element = page.locator(".excel-bar-right > span:nth-child(2)")
    text = element.text_content()
    ts = re.sub("\D", " ", text)
    total_rows = int(ts)
    return total_rows

def download_result_file(page, output_file_path):
    # 点击“下载”按钮并等待下载完成
    page.locator(".next-input-control").first.click()
    page.get_by_role("option", name="UTF-8").click()
    with page.expect_download() as download_info:
        page.get_by_role("button", name="下载").click()
    download = download_info.value
    logger.info(f"目录位置:{download.path()}")
    #print()
    download.save_as(output_file_path)

def close_query_result(page):
    # 关闭查询结果
    result = page.locator('li[role="tab"] >> text=结果[1]')
    result.click()
    right_button = result.locator("../following-sibling::i[1]")
    right_button.click()
    sleep(5)

def process_dataframe(df, log_file_path):
    #logger.remove()  # Remove the default logger
    logger.add(log_file_path, rotation="500 MB")
    # 使用Playwright连接到浏览器
    playwright = sync_playwright().start()
    browser = playwright.chromium.launch(headless=True,channel='msedge')
    default_context = browser.new_context(ignore_https_errors=True, storage_state="state.json")
    page = default_context.new_page()
    page.goto("https://ide2-cn-hangzhou.data.aliyun.com/?defaultProjectId=66895", timeout=1000000)
    try:
        page.get_by_role("button", name="关闭").click()
    except:
        print("没有关闭")
    # 循环遍历 DataFrame 中的每一行，填写 SQL 查询并执行它
    for i, row in df.iterrows():
        # 填写SQL查询并执行它
        logger.info(f"第{i + 1}个正在执行:{row['rule_name']} ")
        copy_sql_query(page, row["sql"])
        run_sql_query(page)
        # 等待结果加载并获取行数
        js = download_query_result(page)
        if js == -1:  # 没有结果不关闭
            df.loc[i, 'result'] = 'SQL报错'
            logger.info(f"{row['rule_name']}sql报错?")
            continue
        elif js == 10000:  # 10000的重新查询 重新下载
            i = 1
            df.loc[i, 'result'] = '超一万'
            while True:  # 重新查询
                close_query_result(page)  # 先关闭结果
                new_sql = row["sql"] + f""" limit {i},10000"""
                copy_sql_query(page, new_sql)
                run_sql_query(page)
                file_path = f"d:/xzz/{row['rule_name']}_page_{i}.csv"
                if download_query_result(page) != 10000:
                    download_result_file(page, file_path)
                    break
                download_result_file(page, file_path)
                i += 10000
        elif js == 0:
            logger.info(f"{row['rule_name']}  ------没有结果")
        else:  # 其他的直接下载
            download_result_file(page, f"d:/xzz/{row['rule_name']}.csv")
        close_query_result(page)
    df.to_excel(log_file_path + '.xlsx', index=False)
    default_context.storage_state(path="state.json")
    browser.close()

if __name__ == '__main__':
    # 将Excel文件读入pandas dataframe中
    df = pd.read_csv(r'C:\Users\<USER>\Desktop\sql规则1.csv', dtype=str)
    # 将DataFrame切分成多个子DataFrame
    num_processes = 3 # 设置进程数
    chunk_size = len(df) // num_processes
    chunks = [df.iloc[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
    if len(chunks) > num_processes:
        chunks[-2] = pd.concat([chunks[-2], chunks[-1]])
        chunks.pop()
    # 为每个进程分配一个日志文件
    log_file_paths = [f"sql执行-多进程-{i+1}.log" for i in range(num_processes)]
    # 使用进程池并行处理每个子DataFrame
    with Pool(num_processes) as p:
        p.starmap(process_dataframe, zip(chunks, log_file_paths))
